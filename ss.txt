
tag.vue:2289 📤 發送資料: {
  "TagId": "01fa0f04-260e-45c3-9bb6-155934b619b9",
  "SimpleTagName": "KW",
  "Description": "MB-B51電表即時功率",
  "Type": 1,
  "DataType": 4,
  "ValueMultiple": 1,
  "TransferFunctionMax": "",
  "TransferFunctionMin": "",
  "Max": "",
  "Min": "",
  "Status": true,
  "ValueAddress": "400069",
  "DeviceId": "6f915bb6-78e8-4bc7-b138-2b141302320e",
  "MeasurementUnit": 9,
  "InitialValue": "0",
  "DataInterval": 1000,
  "IsReadOnly": true,
  "RegionId": "d7b1e25e-0e23-4a50-a0b4-8d539a9973fd",
  "CCTVIdList": [],
  "TagCategoryIdList": [],
  "IgnoreThreshold": null,
  "Retentive": false,
  "SaveType": 0,
  "IsLog": true,
  "LogInterval": 8,
  "LogIntervalType": 2,
  "RelatedPageId": null,
  "AlarmStatus": 1,
  "IsAlarmAudio": false,
  "AlarmSOP": "<p>1221323432</p>",
  "AlarmNotifyGroupList": [],
  "HHStatus": true,
  "HHValue": "8888",
  "HHContent": "",
  "HIStatus": true,
  "HIValue": "222",
  "HIContent": "3323232",
  "LOStatus": true,
  "LOValue": "333",
  "LOContent": "4444",
  "LLStatus": true,
  "LLValue": "999",
  "LLContent": "11122344",
  "DigitalAlarmStatus": false,
  "DigitalAlarmValue": "0",
  "DigitalAlarmContent": "",
  "DigitalNormalStatus": false,
  "DigitalNormalValue": "0",
  "DigitalNormalContent": "",
  "AlarmExceptionStatus": true,
  "AlarmExceptionStartAt": "2025-07-31T17:44:45.000Z",
  "AlarmExceptionEndAt": "2025-07-31T19:45:59.000Z",
  "AlarmExceptionUntil": 2,
  "AlarmExceptionAction": 2,
  "IsUseExpression": false,
  "ExpressMode": 1,
  "ExpressValue": "",
  "Properties": {}
}
tag.vue:2292 📤 重要參數檢查: {Status 型別: 'boolean', Status 值: true, SaveType 型別: 'number', SaveType 值: 0, IsLog 型別: 'boolean', …}
tag.vue:2353 📤 URLSearchParams 內容:
tag.vue:2355   TagId: 01fa0f04-260e-45c3-9bb6-155934b619b9
tag.vue:2355   SimpleTagName: KW
tag.vue:2355   Description: MB-B51電表即時功率
tag.vue:2355   Type: 1
tag.vue:2355   DataType: 4
tag.vue:2355   ValueMultiple: 1
tag.vue:2355   TransferFunctionMax: 
tag.vue:2355   TransferFunctionMin: 
tag.vue:2355   Max: 
tag.vue:2355   Min: 
tag.vue:2355   Status: true
tag.vue:2355   ValueAddress: 400069
tag.vue:2355   DeviceId: 6f915bb6-78e8-4bc7-b138-2b141302320e
tag.vue:2355   MeasurementUnit: 9
tag.vue:2355   InitialValue: 0
tag.vue:2355   DataInterval: 1000
tag.vue:2355   IsReadOnly: true
tag.vue:2355   RegionId: d7b1e25e-0e23-4a50-a0b4-8d539a9973fd
tag.vue:2355   Retentive: false
tag.vue:2355   SaveType: 0
tag.vue:2355   IsLog: true
tag.vue:2355   LogInterval: 8
tag.vue:2355   LogIntervalType: 2
tag.vue:2355   AlarmStatus: 1
tag.vue:2355   IsAlarmAudio: false
tag.vue:2355   AlarmSOP: <p>1221323432</p>
tag.vue:2355   HHStatus: true
tag.vue:2355   HHValue: 8888
tag.vue:2355   HHContent: 
tag.vue:2355   HIStatus: true
tag.vue:2355   HIValue: 222
tag.vue:2355   HIContent: 3323232
tag.vue:2355   LOStatus: true
tag.vue:2355   LOValue: 333
tag.vue:2355   LOContent: 4444
tag.vue:2355   LLStatus: true
tag.vue:2355   LLValue: 999
tag.vue:2355   LLContent: 11122344
tag.vue:2355   DigitalAlarmStatus: false
tag.vue:2355   DigitalAlarmValue: 0
tag.vue:2355   DigitalAlarmContent: 
tag.vue:2355   DigitalNormalStatus: false
tag.vue:2355   DigitalNormalValue: 0
tag.vue:2355   DigitalNormalContent: 
tag.vue:2355   AlarmExceptionStatus: true
tag.vue:2355   AlarmExceptionStartAt: 2025-07-31T17:44:45.000Z
tag.vue:2355   AlarmExceptionEndAt: 2025-07-31T19:45:59.000Z
tag.vue:2355   AlarmExceptionUntil: 2
tag.vue:2355   AlarmExceptionAction: 2
tag.vue:2355   IsUseExpression: false
tag.vue:2355   ExpressMode: 1
tag.vue:2355   ExpressValue: 
tag.vue:2359 🚀 即將發送 API 請求: {API 端點: '/api/Tag/UpdateTag', 請求方法: 'POST', Content-Type: 'application/x-www-form-urlencoded', 數據格式: 'URLSearchParams', 關鍵警報參數: {…}}
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...
tag.vue:2376 📥 API 響應: {Message: '', ReturnCode: 1, Detail: {…}}
tag.vue:2377 📥 API 響應類型: object
tag.vue:2378 📥 API 響應 Detail: {}
tag.vue:2379 📥 API 響應 ReturnCode: 1
tag.vue:2380 📥 API 響應 Message: 
tag.vue:2383 🔍 詳細 API 響應分析: {響應是否存在: true, 響應結構: Array(3), Detail 是否存在: true, Detail 結構: Array(0), ReturnCode 值: 1, …}
tag.vue:2396 🔍 API 響應中的警報數據: {Detail 類型: 'object', Detail 是否為物件: true, Detail 的所有屬性: Array(0), Alarm 物件: undefined, Alarm 物件類型: 'undefined', …}
tag.vue:2411 🔍 檢查成功條件: {response 存在: true, ReturnCode: 1, ReturnCode 類型: 'number', ReturnCode === 1: true, ReturnCode === "1": false}
tag.vue:2420 ✅ 保存成功，API 響應: {Message: '', ReturnCode: 1, Detail: {…}}
tag.vue:2431 ⚠️ API 響應 Detail 為空，需要重新載入測點列表
tag.vue:2434 🔍 與舊系統 API 調用對比分析:
tag.vue:2435 📋 舊系統 API 調用格式:
tag.vue:2436   - URL: /api/Tag/UpdateTag
tag.vue:2437   - Method: POST
tag.vue:2438   - Content-Type: application/x-www-form-urlencoded
tag.vue:2439   - 警報參數格式: HHStatus=true&HHValue=111&HHContent=2222
tag.vue:2440 📋 新系統 API 調用格式:
tag.vue:2441   - URL: /api/Tag/UpdateTag
tag.vue:2442   - Method: POST
tag.vue:2443   - Content-Type: application/x-www-form-urlencoded
tag.vue:2444   - 警報參數實際值: {HHStatus: 'true', HHValue: '8888', HHContent: ''}
tag.vue:2452 🔍 關鍵參數檢查:
tag.vue:2455   - TagId: 01fa0f04-260e-45c3-9bb6-155934b619b9 (類型: string)
tag.vue:2455   - HHStatus: true (類型: string)
tag.vue:2455   - HHValue: 8888 (類型: string)
tag.vue:2455   - HHContent:  (類型: string)
tag.vue:2455   - AlarmStatus: 1 (類型: string)
tag.vue:2459 🔍 完整的 URLSearchParams 字符串: TagId=01fa0f04-260e-45c3-9bb6-155934b619b9&SimpleTagName=KW&Description=MB-B51%E9%9B%BB%E8%A1%A8%E5%8D%B3%E6%99%82%E5%8A%9F%E7%8E%87&Type=1&DataType=4&ValueMultiple=1&TransferFunctionMax=&TransferFunctionMin=&Max=&Min=&Status=true&ValueAddress=400069&DeviceId=6f915bb6-78e8-4bc7-b138-2b141302320e&MeasurementUnit=9&InitialValue=0&DataInterval=1000&IsReadOnly=true&RegionId=d7b1e25e-0e23-4a50-a0b4-8d539a9973fd&Retentive=false&SaveType=0&IsLog=true&LogInterval=8&LogIntervalType=2&AlarmStatus=1&IsAlarmAudio=false&AlarmSOP=%3Cp%3E1221323432%3C%2Fp%3E&HHStatus=true&HHValue=8888&HHContent=&HIStatus=true&HIValue=222&HIContent=3323232&LOStatus=true&LOValue=333&LOContent=4444&LLStatus=true&LLValue=999&LLContent=11122344&DigitalAlarmStatus=false&DigitalAlarmValue=0&DigitalAlarmContent=&DigitalNormalStatus=false&DigitalNormalValue=0&DigitalNormalContent=&AlarmExceptionStatus=true&AlarmExceptionStartAt=2025-07-31T17%3A44%3A45.000Z&AlarmExceptionEndAt=2025-07-31T19%3A45%3A59.000Z&AlarmExceptionUntil=2&AlarmExceptionAction=2&IsUseExpression=false&ExpressMode=1&ExpressValue=
tag.vue:2462 🔍 模擬舊系統請求格式測試:
tag.vue:2497 🔧 保存時強制修正 MB-B51電表.KW 測點單位: {測點名稱: 'KW', 測點說明: 'MB-B51電表即時功率', 原始 tagForm.unit: '9', 強制使用單位: '9'}
tag.vue:2580 🔍 舊系統格式請求字符串: TagId=01fa0f04-260e-45c3-9bb6-155934b619b9&Status=true&RegionId=d7b1e25e-0e23-4a50-a0b4-8d539a9973fd&DeviceId=6f915bb6-78e8-4bc7-b138-2b141302320e&SimpleTagName=KW&Description=MB-B51%E9%9B%BB%E8%A1%A8%E5%8D%B3%E6%99%82%E5%8A%9F%E7%8E%87&ValueAddress=400069&MeasurementUnit=9&DataType=4&InitialValue=0&IgnoreThreshold=&Type=1&Properties=%7B%22Usage%22%3A%22Normal%22%2C%22ContactType%22%3A%22NO%22%7D&Retentive=false&SaveType=0&IsLog=true&DataInterval=1000&LogInterval=8&LogIntervalType=2&AlarmStatus=1&IsAlarmAudio=false&AlarmNotifyGroupList=&AlarmSop=%3Cp%3E1221323432%3C%2Fp%3E&AlarmExceptionStatus=true&AlarmExceptionStartAt=2025-07-31T17%3A44%3A45.000Z&AlarmExceptionEndAt=2025-07-31T19%3A45%3A59.000Z&AlarmExceptionUntil=2&AlarmExceptionAction=2&HHContent=&HHStatus=true&HHValue=8888&HIContent=3323232&HIStatus=true&HIValue=222&LLContent=11122344&LLStatus=true&LLValue=999&LOContent=4444&LOStatus=true&LOValue=333&RelatedPageId=&DigitalAlarmStatus=false&DigitalAlarmValue=0&DigitalAlarmContent=&DigitalNormalStatus=false&DigitalNormalValue=0&DigitalNormalContent=&IsUseExpression=false&ExpressMode=1&ExpressValue=
tag.vue:2581 🔍 舊系統格式關鍵警報參數: {HHStatus: 'true', HHValue: '8888', HHContent: ''}
tag.vue:2588 🚀 嘗試使用舊系統格式發送請求...
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...
tag.vue:2591 ✅ 舊系統格式請求成功: {Message: '', ReturnCode: 1, Detail: {…}}
tag.vue:2594 🎉 舊系統格式請求成功！問題可能在於參數格式或順序
tag.vue:2607 🔍 記錄最後編輯的測點 ID: 01fa0f04-260e-45c3-9bb6-155934b619b9
tag.vue:2728 🔄 已清除用戶修改追蹤，確保下次編輯時能正確載入最新數據
tag.vue:2621 🔄 立即重新載入所有測點數據，模仿舊系統做法...
tag.vue:2625 🗑️ 已清除所有快取數據
tag.vue:3043 🔍 上次刷新時間: 2025-08-01T01:28:22.982514
tag.vue:3049 🔍 當前 customer_id: fdff1878-a54a-44ee-b82c-a62bdc5cdb55
tag.vue:3050 🔍 當前 brand_id: fdff1878-a54a-44ee-b82c-a62bdc5cdb55
tag.vue:3051 🔍 當前 PLC_CUSTOMER_ID: null
tag.vue:3060 🔄 強制重新載入或無快取時間，不傳遞 TagRalatedItemsUpdateTime 參數
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...

tag.vue:3065 🔍 GetTagList API 完整響應: {Message: '', ReturnCode: 1, Detail: {…}}
tag.vue:3066 🔍 API ReturnCode: 1 (1=成功, 2=錯誤)
tag.vue:3067 🔍 API Message: 
tag.vue:3068 🔍 API Detail 是否存在: true
tag.vue:3084 🔍 測點類型列表: (3) [{…}, {…}, {…}]
tag.vue:3088 🔍 存取類型列表: (2) [{…}, {…}]
tag.vue:3092 🔍 地區階層列表: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
tag.vue:3096 🔍 測點分類階層列表: (2) [{…}, {…}]
tag.vue:3111 🔍 同步更新 tagClassTree: Proxy(Array) {0: {…}, 1: {…}}
tag.vue:3115 分類 0: {id: 'db08b5ef-05d0-4326-92a6-b5851d45569b', name: 'gg', children: Proxy(Array)}
tag.vue:3115 分類 1: {id: '732f8a3d-a419-4a64-8649-f7bce521c4b8', name: 'AAAAA', children: Proxy(Array)}
tag.vue:3131 🔍 CCTV 對應列表: (2) [{…}, {…}]
tag.vue:3140 🔍 CheckTagRalatedItemsUpdateTimeOK: false
tag.vue:3148 🔍 檢查 detail.TagList: (258) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, 
tag.vue:3149 🔍 detail.TagList 是否存在: true
tag.vue:3150 🔍 detail.TagList 長度: 258
tag.vue:3163 🔍 測點列表數量: 258
tag.vue:3164 🔍 第一個測點的完整原始數據: {Id: '01fa0f04-260e-45c3-9bb6-155934b619b9', CurrentValue: null, Quality: null, QualityText: null, CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55', …}
tag.vue:3173 🔍 找到 MB-B51電表 測點: {Id: '01fa0f04-260e-45c3-9bb6-155934b619b9', CurrentValue: null, Quality: null, QualityText: null, CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55', …}
tag.vue:3174   ===== MB-B51電表即時功率 測點數值對比 =====
tag.vue:3175 🏷️  測點基本信息:
tag.vue:3176    - 測點ID: 01fa0f04-260e-45c3-9bb6-155934b619b9
tag.vue:3177    - 測點名稱: 高雄火車站商業大樓電力管理系統通道.MB-B51電表.KW
tag.vue:3178    - 說明: MB-B51電表即時功率
tag.vue:3179    - PLC位址: 400069 (舊系統: 400069)
tag.vue:3181   測點配置:
tag.vue:3182    - 測點種類 ID: 1 名稱: 類比測點 (舊系統: 類比測點)
tag.vue:3183    - 資料型別 ID: 4 名稱: Short (舊系統: Short)
tag.vue:3184    - 測量單位 ID: 9 名稱: kW (舊系統: kW)
tag.vue:3185    - 存取類型 ID: 0 名稱: 唯讀 (舊系統: 可讀也可寫)
tag.vue:3186    - 儲存歷史: true (舊系統: 儲存)
tag.vue:3187    - 儲存間隔: 8 分鐘
tag.vue:3188    - 儲存間隔類型: {Id: 2, Name: '定時'}
tag.vue:3190 🏢 關聯信息:
tag.vue:3191    - 裝置 ID: 6f915bb6-78e8-4bc7-b138-2b141302320e 名稱: 高雄火車站商業大樓電力管理系統通道.MB-B51電表 (舊系統: MB-B51電表)
tag.vue:3192    - 地區列表: [{…}] (舊系統: 無)
tag.vue:3193    - CCTV列表: [] (舊系統: 包含 0 個CCTV)
tag.vue:3194    - 測點分類列表: [] (舊系統: 請選擇)
tag.vue:3196 ⚠️  警報設定:
tag.vue:3197    - 警報配置: {Status: 1, Audio: false, Sop: '<p>1221323432</p>', NotifyGroup: Array(0), HHStatus: true, …}
tag.vue:3198 📊 ============================================
tag.vue:3207 🔍 找到剛才編輯的測點: {Id: '01fa0f04-260e-45c3-9bb6-155934b619b9', CurrentValue: null, Quality: null, QualityText: null, CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55', …}
tag.vue:3208 🎯 ===== 剛才編輯的測點警報檢查 =====
tag.vue:3209 🏷️  測點基本信息:
tag.vue:3210    - 測點ID: 01fa0f04-260e-45c3-9bb6-155934b619b9
tag.vue:3211    - 測點名稱: 高雄火車站商業大樓電力管理系統通道.MB-B51電表.KW
tag.vue:3212    - 說明: MB-B51電表即時功率
tag.vue:3214 ⚠️  警報設定檢查:
tag.vue:3215    - 警報配置: {Status: 1, Audio: false, Sop: '<p>1221323432</p>', NotifyGroup: Array(0), HHStatus: true, …}
tag.vue:3217 🔍 警報詳細檢查:
tag.vue:3218    - HH 警報: Status= true , Value= 8888 , Content= 
tag.vue:3219    - HI 警報: Status= true , Value= 222 , Content= 3323232
tag.vue:3220    - LO 警報: Status= true , Value= 333 , Content= 4444
tag.vue:3221    - LL 警報: Status= true , Value= 999 , Content= 11122344
tag.vue:3225 📊 ============================================
tag.vue:3232 🔍 準備獲取測點屬性，測點數量: 258
tags.ts:371 🔍 準備調用 GetTagProperties API: {url: '/Tag/GetTagProperties', totalTagCount: 258, batchSize: 50, batchCount: 6}
tags.ts:385 🔍 處理第 1/6 批次: {batchTagIds: 50, tagsHeader: '01fa0f04-260e-45c3-9bb6-155934b619b9,0472000b-80c6…b-b04c-80f146cd7ec9,04b9341b-75ae-4999-b095-ae...'}
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...
tags.ts:395 🔍 第 1 批次 API 響應: {hasDetail: true, hasProperties: true, returnCode: 1, message: '', propertiesCount: 21}
tags.ts:385 🔍 處理第 2/6 批次: {batchTagIds: 50, tagsHeader: '3392afc0-a15c-42c6-96d2-17d66fefc21c,3403902e-d0a0…0-a735-8e08547fe750,35828683-a061-404e-b9e5-41...'}
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...
tags.ts:395 🔍 第 2 批次 API 響應: {hasDetail: true, hasProperties: true, returnCode: 1, message: '', propertiesCount: 50}
tags.ts:385 🔍 處理第 3/6 批次: {batchTagIds: 50, tagsHeader: '6971fa0e-3673-4cea-bc59-20ffd8b0b213,6a568708-396d…1-b54b-f115f3338473,6b9d1fdd-a711-41ad-b931-0e...'}
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...
tags.ts:395 🔍 第 3 批次 API 響應: {hasDetail: true, hasProperties: true, returnCode: 1, message: '', propertiesCount: 50}
tags.ts:385 🔍 處理第 4/6 批次: {batchTagIds: 50, tagsHeader: '96b5b407-34cd-424f-98a3-c6166627bb65,994d7ab7-ab8a…2-b880-4e147bbf9dd2,9a52e27a-b9e1-473f-9658-4e...'}
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...
tags.ts:395 🔍 第 4 批次 API 響應: {hasDetail: true, hasProperties: true, returnCode: 1, message: '', propertiesCount: 50}
tags.ts:385 🔍 處理第 5/6 批次: {batchTagIds: 50, tagsHeader: 'c6e85075-b192-43c6-8d0e-17f45ba6d06f,c714c1d6-5c7b…7-9324-cfecd2213848,cb1630d1-579d-4ffa-ba6d-d1...'}
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...
tags.ts:395 🔍 第 5 批次 API 響應: {hasDetail: true, hasProperties: true, returnCode: 1, message: '', propertiesCount: 50}
tags.ts:385 🔍 處理第 6/6 批次: {batchTagIds: 8, tagsHeader: 'fc8ddd9d-8041-4d43-9087-252d7028968d,fd8a28b0-9a8c…7-aa18-30c98cd8569f,fa7c2ee6-c1b5-4cf6-bff0-ff...'}
http.ts:79 使用舊前端token: eyJhbGciOiJIUzI1NiIs...
tags.ts:395 🔍 第 6 批次 API 響應: {hasDetail: true, hasProperties: true, returnCode: 1, message: '', propertiesCount: 3}
tags.ts:419 🔍 所有批次處理完成，合併結果: {totalProperties: 224, requestedTags: 258}
tag.vue:3238 🔍 測點屬性獲取成功: {257b0e49-6a90-4830-a7a8-8b7beec294ef: {…}, 25b65a52-fc9b-4bde-b3d7-63e034b7c304: {…}, 271ec3ba-eef6-49ea-9f5c-996f3845b7d8: {…}, 27f7199d-eb4e-4232-9e2f-4b50fbe44431: {…}, 281da786-76ac-40dd-bf9c-ce553e44ef4c: {…}, …}
tag.vue:3245 🔍 開始轉換測點數據，原始數量: 258
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: '01fa0f04-260e-45c3-9bb6-155934b619b9', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: '21ffcabf-218d-4215-9cf9-f769639e079c', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: '230f8b3e-d8dd-4418-bccb-066519121e2a', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: '36d04260-c07a-40e2-abfa-a03e37679a68', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: '397aa9d2-089b-42a5-a29f-b1f62757e8cc', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: '43b4fa3c-8b6e-4317-8c41-2f7278aaa669', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: '7885a6c7-be0d-46d8-b0ff-fad43e173fee', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: 'a27eb120-f441-45a4-ba72-62dab70883f8', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: 'af3d4fa5-3740-49ec-9ab6-c9934ad1b97f', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:3257 🔍 處理 MB-B51電表 測點數據轉換: {測點ID: 'b588501c-7ba9-42ce-b1fe-6e64efc24e79', 原始設備名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表', 原始CCTV列表: Array(0), 原始測量單位: {…}, 原始地區列表: Array(1), …}
tag.vue:1161 🔍 filteredTagList 計算中: {原始 tagList 長度: 258, 搜尋關鍵字: '', 狀態篩選: '', 類型篩選: '', 裝置篩選: ''}
tag.vue:1196 🔍 最終 filteredTagList 長度: 258
tag.vue:3329 ✅ 測點數據轉換完成: {原始測點數量: 258, 轉換後測點數量: 258, 第一個轉換後的測點: Proxy(Object), filteredTagList 長度: 258}
tag.vue:2629 ✅ 立即載入完成
tag.vue:2636 🔍 檢查更新後的警報數據:
tag.vue:2637    - HH 警報: Status= true , Value= 8888 , Content= 
tag.vue:2638    - HI 警報: Status= true , Value= 222 , Content= 3323232
tag.vue:2639    - LO 警報: Status= true , Value= 333 , Content= 4444
tag.vue:2640    - LL 警報: Status= true , Value= 999 , Content= 11122344
tag.vue:2645 ✅ 警報數據已正確更新
tag.vue:2652 🔍 保留 lastEditedTagId 標記用於調試追蹤
tag.vue:2657 🔧 調試：嘗試直接查詢 TagAlarmRule 表...
tag.vue:2673 🔍 調試 SQL 查詢: 
            SELECT
              "TagId",
              "Status",
              "Priority",
              "AlarmValue",
              "Description",
              "UpdateTime"
            FROM "TagAlarmRule"
            WHERE "TagId" = '01fa0f04-260e-45c3-9bb6-155934b619b9'
            ORDER BY "Priority"
          
tag.vue:2674 💡 建議後端開發人員執行此查詢來檢查數據是否正確保存
tag.vue:2677 🔍 嘗試獲取測點詳細信息...
tag.vue:2680 📋 測點詳細信息: {測點ID: '01fa0f04-260e-45c3-9bb6-155934b619b9', 測點名稱: 'KW', 完整警報物件: Proxy(Object), 警報狀態: true, 原始數據: Proxy(Object)}
tag.vue:1413 editTag 被調用: Proxy(Object) {id: '01fa0f04-260e-45c3-9bb6-155934b619b9', name: 'KW', simpleName: 'KW', description: 'MB-B51電表即時功率', tagType: '類比測點', …}
tag.vue:1414 測點資料: Proxy(Object) {id: '01fa0f04-260e-45c3-9bb6-155934b619b9', name: 'KW', simpleName: 'KW', description: 'MB-B51電表即時功率', tagType: '類比測點', …}
tag.vue:2728 🔄 已清除用戶修改追蹤，確保下次編輯時能正確載入最新數據
tag.vue:1434 🔍 editTag 中的 isReadOnly 設定: {row.isReadOnly: true, tagForm.isReadOnly: true}
tag.vue:1498 找到原始測點資料: Proxy(Object) {Id: '01fa0f04-260e-45c3-9bb6-155934b619b9', CurrentValue: null, Quality: null, QualityText: null, CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55', …}
tag.vue:1542 🔧 強制修正 MB-B51電表.KW 測點單位: {測點名稱: '高雄火車站商業大樓電力管理系統通道.MB-B51電表.KW', 原始單位ID: 9, 原始單位名稱: 'kW', 修正後單位ID: 9, 修正後單位名稱: 'kW'}
tag.vue:1574 裝置欄位調試: {originalData.Device: Proxy(Object), originalData.DeviceId: undefined, row.deviceId: '6f915bb6-78e8-4bc7-b138-2b141302320e', 處理後 deviceId: '6f915bb6-78e8-4bc7-b138-2b141302320e', 最終 tagForm.deviceId: '6f915bb6-78e8-4bc7-b138-2b141302320e'}
tag.vue:1584 最終 deviceId 設定: 6f915bb6-78e8-4bc7-b138-2b141302320e 類型: string
tag.vue:1627 🔍 測點分類處理（與舊系統一致：只選最底層）: {原始 TagCategoryList: Proxy(Array), 層級路徑分析: Array(0), 提取的最底層 categoryIds: Array(0), 設定的 tagCategoryIds: Proxy(Array), 當前 tagClassTree 完整內容: '[\n  {\n    "id": "db08b5ef-05d0-4326-92a6-b5851d455…8",\n    "name": "AAAAA",\n    "children": []\n  }\n]', …}
tag.vue:1677 原始 Status 值: true 類型: boolean
tag.vue:1679 轉換後 Status 值: true 類型: boolean
tag.vue:1683 🔍 SaveType 調試資訊: {originalData.SaveType: Proxy(Object), SaveType 類型: 'object', row.isReadOnly: true, SaveType 是否為對象: true, 當前 tagForm.saveType: 1, …}
tag.vue:1696 ✅ 載入後端數據，使用 originalData.SaveType.Id: 0
tag.vue:1709 🔍 IsLog 調試資訊: {originalData.IsLog: true, IsLog 類型: 'boolean'}
tag.vue:1714 ✅ 設定 tagForm.log: true
tag.vue:1724 🔍 存取權限和儲存歷史設定: {SaveType.Id: 0, SaveType.Name: '唯讀', IsLog: true, LogInterval: 8, LogInterValType: Proxy(Object), …}
tag.vue:1742 原始 AlarmSop 值: undefined 類型: undefined
tag.vue:1743 原始 Alarm.Sop 值: <p>1221323432</p> 類型: string
tag.vue:1744 原始資料完整物件: Proxy(Object) {Id: '01fa0f04-260e-45c3-9bb6-155934b619b9', CurrentValue: null, Quality: null, QualityText: null, CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55', …}
tag.vue:1745 🔍 原始 Alarm 物件詳細: {Alarm 物件: Proxy(Object), Alarm 類型: 'object', Alarm 是否為 null: false, Alarm 是否為 undefined: false, Alarm 的所有屬性: Array(23)}
tag.vue:1755 🔍 Alarm 物件完整內容: Proxy(Object) {Status: 1, Audio: false, Sop: '<p>1221323432</p>', NotifyGroup: Array(0), HHStatus: true, …}
tag.vue:1756 🔍 Alarm 物件所有屬性名稱: (23) ['Status', 'Audio', 'Sop', 'NotifyGroup', 'HHStatus', 'HHValue', 'HHContent', 'HIStatus', 'HIValue', 'HIContent', 'LLStatus', 'LLValue', 'LLContent', 'LOStatus', 'LOValue', 'LOContent', 'DigAlarmStatus', 'DigAlarmValue', 'DigAlarmContent', 'DigNormalStatus', 'DigNormalValue', 'DigNormalContent', 'AlarmException']
tag.vue:1757 🔍 Alarm 物件各屬性值: {Status: 1, Audio: false, Sop: '<p>1221323432</p>', NotifyGroup: Proxy(Array), HHStatus: true, …}
tag.vue:1781 🔧 從 HTML 格式提取 SOP 內容: <p>1221323432</p> -> 1221323432
tag.vue:1800 🔍 警報值載入調試: {原始 HHValue: '8888', 原始 HHStatus: true, 原始 HHContent: '', 原始 HIValue: '222', 原始 HIStatus: true, …}
tag.vue:1816 🔍 parseAlarmValue 函數調試:
tag.vue:1822   HHValue 處理過程: {原始值: '8888', 原始值類型: 'string', 是否為空: false, 是否等於 -1000000000000000000000000: false, 是否等於 -1e+24: false, …}
tag.vue:1833   HIValue 處理過程: {原始值: '222', 原始值類型: 'string', 是否為空: false, 是否等於 -1000000000000000000000000: false, 是否等於 -1e+24: false, …}
tag.vue:1844   LOValue 處理過程: {原始值: '333', 原始值類型: 'string', 是否為空: false, 是否等於 -1000000000000000000000000: false, 是否等於 -1e+24: false, …}
tag.vue:1855   LLValue 處理過程: {原始值: '999', 原始值類型: 'string', 是否為空: false, 是否等於 -1000000000000000000000000: false, 是否等於 -1e+24: false, …}
tag.vue:1889 ✅ 載入後的警報值: {HH: {…}, HI: {…}, LO: {…}, LL: {…}}
tag.vue:1926 🔍 例外設定載入調試: {Alarm 物件存在: true, AlarmException 物件存在: false, AlarmException 完整內容: null, AlarmException 類型: 'object'}
tag.vue:1986 ⚠️ 沒有 AlarmException 物件，使用預設值
tag.vue:2000 🔍 測點屬性設定: {測點用途 (usage): '', 接點種類 (closingContact): '', 來源 row.usage: '', 來源 row.contactType: ''}
tag.vue:2015 tagForm 已填充: Proxy(Object) {id: '01fa0f04-260e-45c3-9bb6-155934b619b9', name: 'KW', description: 'MB-B51電表即時功率', tagType: 'Analog', dataType: 'Short', …}
