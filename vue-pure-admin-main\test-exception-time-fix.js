/**
 * 測試例外設定時間修復效果
 * 
 * 問題描述：
 * 例外設定時間在新舊系統間格式不一致，導致後端解析錯誤
 * 
 * 舊系統格式：
 * - 前端發送：HH:mm:ss 格式字符串
 * - 後端接收：DateTime 類型
 * 
 * 新系統修復前：
 * - 前端發送：JavaScript Date 物件字符串表示
 * - 後端可能解析失敗
 * 
 * 修復方案：
 * 1. 保存時：轉換為 HH:mm:ss 格式
 * 2. 載入時：從 HH:mm:ss 格式轉換為 Date 物件
 */

// 簡單的 dayjs 模擬函數
function dayjs(date, format) {
  if (typeof date === 'string' && format === 'HH:mm:ss') {
    // 解析 HH:mm:ss 格式
    const [hours, minutes, seconds] = date.split(':')
    const d = new Date()
    d.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
    return {
      format: (fmt) => {
        if (fmt === 'HH:mm:ss') {
          return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`
        }
        return d.toString()
      }
    }
  }

  const d = date ? new Date(date) : new Date()
  return {
    format: (fmt) => {
      if (fmt === 'HH:mm:ss') {
        return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`
      }
      return d.toString()
    }
  }
}

console.log('🧪 例外設定時間修復測試開始...')

// 模擬舊系統的處理方式
function simulateOldSystem() {
  console.log('\n📋 舊系統處理方式:')
  
  // 模擬前端表單數據（時間選擇器的值）
  const formData = {
    exceptionStartTime: new Date('2025-07-31T16:18:18.000Z'),
    exceptionEndTime: new Date('2025-07-31T17:18:18.000Z')
  }
  
  console.log('1. 前端表單數據:', {
    startTime: formData.exceptionStartTime.toString(),
    endTime: formData.exceptionEndTime.toString()
  })
  
  // 舊系統的轉換邏輯
  const oldSystemFormat = {
    exceptionStartAt: formData.exceptionStartTime && 
      dayjs(formData.exceptionStartTime).format("HH:mm:ss"),
    exceptionEndAt: formData.exceptionEndTime && 
      dayjs(formData.exceptionEndTime).format("HH:mm:ss")
  }
  
  console.log('2. 舊系統發送格式:', oldSystemFormat)
  
  // 模擬從 API 響應載入
  const apiResponse = {
    StartAt: "16:18:18",
    EndAt: "17:18:18"
  }
  
  console.log('3. API 響應格式:', apiResponse)
  
  // 舊系統的載入邏輯
  const loadedData = {
    exceptionStartAt: apiResponse.StartAt && dayjs(apiResponse.StartAt, "HH:mm:ss"),
    exceptionEndAt: apiResponse.EndAt && dayjs(apiResponse.EndAt, "HH:mm:ss")
  }
  
  console.log('4. 舊系統載入結果:', {
    startTime: loadedData.exceptionStartAt?.format('HH:mm:ss'),
    endTime: loadedData.exceptionEndAt?.format('HH:mm:ss')
  })
}

// 模擬新系統修復前的問題
function simulateNewSystemBefore() {
  console.log('\n❌ 新系統修復前的問題:')
  
  // 模擬前端表單數據
  const formData = {
    exceptionStartTime: new Date('2025-07-31T16:18:18.000Z'),
    exceptionEndTime: new Date('2025-07-31T17:18:18.000Z')
  }
  
  console.log('1. 前端表單數據:', {
    startTime: formData.exceptionStartTime.toString(),
    endTime: formData.exceptionEndTime.toString()
  })
  
  // 修復前的錯誤格式（發送 Date 物件）
  const beforeFixFormat = {
    AlarmExceptionStartAt: formData.exceptionStartTime,
    AlarmExceptionEndAt: formData.exceptionEndTime
  }
  
  console.log('2. 修復前發送格式（有問題）:', {
    startTime: beforeFixFormat.AlarmExceptionStartAt.toString(),
    endTime: beforeFixFormat.AlarmExceptionEndAt.toString()
  })
  
  console.log('3. 問題：後端可能無法正確解析 JavaScript Date 物件的字符串表示')
}

// 模擬新系統修復後的正確處理
function simulateNewSystemAfter() {
  console.log('\n✅ 新系統修復後的正確處理:')
  
  // 模擬前端表單數據
  const formData = {
    exceptionStartTime: new Date('2025-07-31T16:18:18.000Z'),
    exceptionEndTime: new Date('2025-07-31T17:18:18.000Z')
  }
  
  console.log('1. 前端表單數據:', {
    startTime: formData.exceptionStartTime.toString(),
    endTime: formData.exceptionEndTime.toString()
  })
  
  // 修復後的正確格式（與舊系統一致）
  const afterFixFormat = {
    AlarmExceptionStartAt: formData.exceptionStartTime ? 
      dayjs(formData.exceptionStartTime).format("HH:mm:ss") : null,
    AlarmExceptionEndAt: formData.exceptionEndTime ? 
      dayjs(formData.exceptionEndTime).format("HH:mm:ss") : null
  }
  
  console.log('2. 修復後發送格式（正確）:', afterFixFormat)
  
  // 模擬載入邏輯
  const apiResponse = "16:18:18"
  
  console.log('3. API 響應格式:', apiResponse)
  
  // 修復後的載入邏輯
  function parseTimeString(timeStr) {
    if (!timeStr || !timeStr.includes(':')) return null
    
    const startTime = new Date()
    const [hours, minutes, seconds] = timeStr.split(':')
    startTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
    return startTime
  }
  
  const loadedTime = parseTimeString(apiResponse)
  
  console.log('4. 修復後載入結果:', {
    原始字符串: apiResponse,
    轉換後時間: loadedTime?.toString(),
    格式化顯示: dayjs(loadedTime).format('HH:mm:ss')
  })
}

// 執行測試
simulateOldSystem()
simulateNewSystemBefore()
simulateNewSystemAfter()

console.log('\n🎯 修復總結:')
console.log('1. 保存時：使用 dayjs().format("HH:mm:ss") 轉換為時間字符串')
console.log('2. 載入時：從 HH:mm:ss 字符串轉換為當天的 Date 物件')
console.log('3. 確保與舊系統完全一致的時間處理邏輯')

console.log('\n✅ 例外設定時間修復測試完成！')
